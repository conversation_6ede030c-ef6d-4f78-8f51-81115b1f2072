#!/usr/bin/env python3
"""
Setup F5-TTS Training Configuration for Nepali Dataset
"""

import os
import json
import yaml
from pathlib import Path

def create_training_config():
    """Create training configuration for F5-TTS fine-tuning."""
    
    config = {
        # Model configuration
        "model": {
            "name": "F5-TTS",
            "vocab_size": 56,  # Based on our Nepali vocabulary
            "hidden_dim": 1024,
            "num_layers": 12,
            "num_heads": 16,
            "dropout": 0.1,
        },
        
        # Training configuration
        "training": {
            "learning_rate": 1e-4,
            "batch_size": 8,  # Adjust based on GPU memory
            "max_epochs": 10,
            "warmup_steps": 1000,
            "gradient_clip_val": 1.0,
            "accumulate_grad_batches": 1,
            "save_per_epochs": 2,
            "validation_split": 0.1,
        },
        
        # Data configuration
        "data": {
            "dataset_dir": "nepali_dataset_f5tts_prepared",
            "sample_rate": 22050,
            "max_duration": 30.0,  # Maximum audio duration in seconds
            "min_duration": 0.4,   # Minimum audio duration in seconds
            "num_workers": 4,
            "pin_memory": True,
        },
        
        # Output configuration
        "output": {
            "output_dir": "f5tts_nepali_finetune_output",
            "checkpoint_dir": "f5tts_nepali_finetune_output/checkpoints",
            "log_dir": "f5tts_nepali_finetune_output/logs",
            "save_top_k": 3,  # Keep top 3 checkpoints
        },
        
        # Logging configuration
        "logging": {
            "log_every_n_steps": 50,
            "val_check_interval": 0.5,  # Validate every half epoch
            "enable_wandb": False,  # Set to True if you want to use Weights & Biases
            "wandb_project": "f5tts-nepali-finetune",
        },
        
        # Hardware configuration
        "hardware": {
            "gpus": 1,  # Number of GPUs to use
            "precision": 16,  # Use mixed precision training
            "strategy": "auto",  # Training strategy
        }
    }
    
    return config

def save_config(config, config_file):
    """Save configuration to file."""
    config_path = Path(config_file)
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    if config_file.endswith('.yaml') or config_file.endswith('.yml'):
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    else:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"Configuration saved to: {config_file}")

def create_training_script():
    """Create a simplified training script."""
    
    script_content = '''#!/usr/bin/env python3
"""
Simple F5-TTS Training Script for Nepali Dataset
"""

import os
import sys
import json
from pathlib import Path

def main():
    """Main training function."""
    
    # Load configuration
    config_file = "f5tts_training_config.json"
    if not Path(config_file).exists():
        print(f"Configuration file not found: {config_file}")
        print("Please run setup_f5tts_training.py first to create the configuration.")
        return 1
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("🚀 Starting F5-TTS Fine-tuning for Nepali Dataset")
    print(f"📁 Dataset: {config['data']['dataset_dir']}")
    print(f"📁 Output: {config['output']['output_dir']}")
    print(f"🎯 Epochs: {config['training']['max_epochs']}")
    print(f"📊 Batch Size: {config['training']['batch_size']}")
    print(f"🧠 Learning Rate: {config['training']['learning_rate']}")
    
    # Create output directories
    output_dir = Path(config['output']['output_dir'])
    checkpoint_dir = Path(config['output']['checkpoint_dir'])
    log_dir = Path(config['output']['log_dir'])
    
    output_dir.mkdir(parents=True, exist_ok=True)
    checkpoint_dir.mkdir(parents=True, exist_ok=True)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Check if dataset exists
    dataset_dir = Path(config['data']['dataset_dir'])
    if not dataset_dir.exists():
        print(f"❌ Dataset directory not found: {dataset_dir}")
        print("Please prepare the dataset first using f5tts_finetune_nepali.py --prepare_only")
        return 1
    
    # Check for required files
    required_files = ['raw.arrow', 'duration.json', 'vocab.txt']
    missing_files = []
    for file in required_files:
        if not (dataset_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files in dataset: {missing_files}")
        print("Please prepare the dataset first using f5tts_finetune_nepali.py --prepare_only")
        return 1
    
    print("✅ All required files found. Dataset is ready for training.")
    
    try:
        # Import F5-TTS training modules
        from f5_tts.train.finetune_cli import main as finetune_main
        
        # Set up arguments for F5-TTS CLI
        args = [
            "--dataset_dir", str(dataset_dir),
            "--output_dir", str(output_dir),
            "--learning_rate", str(config['training']['learning_rate']),
            "--batch_size", str(config['training']['batch_size']),
            "--max_epochs", str(config['training']['max_epochs']),
            "--save_per_epochs", str(config['training']['save_per_epochs']),
            "--num_workers", str(config['data']['num_workers']),
        ]
        
        print(f"🔧 Training arguments: {' '.join(args)}")
        
        # Run training
        original_argv = sys.argv
        sys.argv = ['train'] + args
        
        try:
            finetune_main()
            print("🎉 Training completed successfully!")
            return 0
        finally:
            sys.argv = original_argv
            
    except ImportError as e:
        print(f"❌ Error importing F5-TTS: {e}")
        print("Make sure F5-TTS is properly installed.")
        return 1
    except Exception as e:
        print(f"❌ Training failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
    
    return script_content

def setup_directories(config):
    """Create necessary directories for training."""
    
    directories = [
        config['output']['output_dir'],
        config['output']['checkpoint_dir'],
        config['output']['log_dir'],
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"📁 Created directory: {directory}")

def main():
    """Main setup function."""
    
    print("🔧 Setting up F5-TTS training configuration for Nepali dataset...")
    
    # Create configuration
    config = create_training_config()
    
    # Save configuration files
    save_config(config, "f5tts_training_config.json")
    save_config(config, "f5tts_training_config.yaml")
    
    # Create training script
    script_content = create_training_script()
    with open("train_f5tts_nepali.py", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # Make script executable
    os.chmod("train_f5tts_nepali.py", 0o755)
    print("📝 Created training script: train_f5tts_nepali.py")
    
    # Setup directories
    setup_directories(config)
    
    print("\n✅ F5-TTS training setup completed!")
    print("\n📋 Next steps:")
    print("1. Prepare your full dataset:")
    print("   ./f5-tts-env/bin/python f5tts_finetune_nepali.py --prepare_only")
    print("\n2. Start training:")
    print("   ./f5-tts-env/bin/python train_f5tts_nepali.py")
    print("\n3. Or use the comprehensive script:")
    print("   ./f5-tts-env/bin/python f5tts_finetune_nepali.py")
    
    print(f"\n📁 Configuration files created:")
    print(f"   - f5tts_training_config.json")
    print(f"   - f5tts_training_config.yaml")
    print(f"   - train_f5tts_nepali.py")

if __name__ == "__main__":
    main()
