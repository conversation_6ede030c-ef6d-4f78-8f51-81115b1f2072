#!/usr/bin/env python3
"""
F5-TTS Fine-tuning Script for Nepali Dataset
"""

import os
import sys
import argparse
from pathlib import Path

def setup_environment():
    """Setup the environment for F5-TTS training."""
    # Add F5-TTS to Python path
    sys.path.append(os.getcwd())
    
    try:
        from f5_tts.train.finetune_cli import main as finetune_main
        return True
    except ImportError as e:
        print(f"Error importing F5-TTS: {e}")
        print("Make sure F5-TTS is properly installed.")
        return False

def prepare_full_dataset(source_dir, output_dir):
    """Prepare the full Nepali dataset for F5-TTS training."""
    print(f"Preparing full dataset from: {source_dir}")
    print(f"Output will be saved to: {output_dir}")
    
    # Import our dataset preparation script
    sys.path.append(os.path.dirname(__file__))
    
    try:
        # First, fix the format of the full dataset
        from prepare_nepali_for_f5tts import convert_thorsten_to_f5tts_format, prepare_f5tts_dataset
        
        # Convert format
        print("Step 1: Converting dataset format...")
        converted_path = convert_thorsten_to_f5tts_format(
            input_dir=source_dir,
            output_dir=f"{source_dir}_f5tts_format"
        )
        
        # Fix metadata format (add wavs/ prefix)
        metadata_file = converted_path / "metadata.csv"
        print("Step 2: Fixing metadata format...")
        
        # Read and fix metadata
        with open(metadata_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        with open(metadata_file, 'w', encoding='utf-8') as f:
            for line in lines:
                line = line.strip()
                if line and '|' in line:
                    parts = line.split('|')
                    filename = parts[0].strip()
                    text = parts[1].strip()
                    if not filename.startswith('wavs/'):
                        filename = f"wavs/{filename}"
                    f.write(f"{filename}|{text}\n")
        
        # Prepare dataset for F5-TTS
        print("Step 3: Preparing dataset for F5-TTS...")
        success = prepare_f5tts_dataset(converted_path, output_dir)
        
        if success:
            # Create vocabulary file
            print("Step 4: Creating vocabulary file...")
            from create_nepali_vocab import create_vocab_from_dataset
            vocab_file = Path(output_dir) / "vocab.txt"
            create_vocab_from_dataset(metadata_file, vocab_file)
            
            print(f"✅ Full dataset prepared successfully!")
            print(f"📁 Prepared dataset location: {output_dir}")
            return True
        else:
            print("❌ Dataset preparation failed!")
            return False
            
    except Exception as e:
        print(f"Error during dataset preparation: {e}")
        return False

def run_finetune(dataset_dir, output_dir, **kwargs):
    """Run F5-TTS fine-tuning."""
    
    if not setup_environment():
        return False
    
    # Set up arguments for fine-tuning
    args = [
        "--dataset_dir", str(dataset_dir),
        "--output_dir", str(output_dir),
        "--model_name", "F5-TTS",
        "--learning_rate", str(kwargs.get('learning_rate', 1e-4)),
        "--batch_size", str(kwargs.get('batch_size', 8)),
        "--max_epochs", str(kwargs.get('max_epochs', 10)),
        "--save_per_epochs", str(kwargs.get('save_per_epochs', 2)),
        "--num_workers", str(kwargs.get('num_workers', 4)),
    ]
    
    # Add optional arguments
    if kwargs.get('resume_from_checkpoint'):
        args.extend(["--resume_from_checkpoint", str(kwargs['resume_from_checkpoint'])])
    
    if kwargs.get('wandb_project'):
        args.extend(["--wandb_project", str(kwargs['wandb_project'])])
    
    print(f"Starting F5-TTS fine-tuning with arguments: {' '.join(args)}")
    
    try:
        # Import and run the fine-tuning CLI
        from f5_tts.train.finetune_cli import main as finetune_main
        
        # Temporarily modify sys.argv to pass arguments
        original_argv = sys.argv
        sys.argv = ['finetune'] + args
        
        try:
            finetune_main()
            print("✅ Fine-tuning completed successfully!")
            return True
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        print(f"Error during fine-tuning: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="F5-TTS Fine-tuning for Nepali Dataset")
    parser.add_argument("--source_dataset", type=str, default="nepali_dataset_thorsten_format",
                       help="Source dataset directory (Thorsten format)")
    parser.add_argument("--prepared_dataset", type=str, default="nepali_dataset_f5tts_prepared",
                       help="Prepared dataset directory for F5-TTS")
    parser.add_argument("--output_dir", type=str, default="f5tts_nepali_finetune_output",
                       help="Output directory for fine-tuned model")
    parser.add_argument("--learning_rate", type=float, default=1e-4,
                       help="Learning rate for fine-tuning")
    parser.add_argument("--batch_size", type=int, default=8,
                       help="Batch size for training")
    parser.add_argument("--max_epochs", type=int, default=10,
                       help="Maximum number of epochs")
    parser.add_argument("--save_per_epochs", type=int, default=2,
                       help="Save checkpoint every N epochs")
    parser.add_argument("--num_workers", type=int, default=4,
                       help="Number of data loading workers")
    parser.add_argument("--resume_from_checkpoint", type=str, default=None,
                       help="Path to checkpoint to resume from")
    parser.add_argument("--wandb_project", type=str, default=None,
                       help="Weights & Biases project name for logging")
    parser.add_argument("--prepare_only", action="store_true",
                       help="Only prepare dataset, don't start training")
    parser.add_argument("--skip_preparation", action="store_true",
                       help="Skip dataset preparation, use existing prepared dataset")
    
    args = parser.parse_args()
    
    try:
        # Step 1: Prepare dataset (unless skipped)
        if not args.skip_preparation:
            print("🔄 Preparing Nepali dataset for F5-TTS...")
            success = prepare_full_dataset(args.source_dataset, args.prepared_dataset)
            if not success:
                print("❌ Dataset preparation failed!")
                return 1
        else:
            print(f"⏭️  Skipping dataset preparation, using: {args.prepared_dataset}")
        
        # Step 2: Run fine-tuning (unless prepare_only)
        if not args.prepare_only:
            print("🚀 Starting F5-TTS fine-tuning...")
            success = run_finetune(
                dataset_dir=args.prepared_dataset,
                output_dir=args.output_dir,
                learning_rate=args.learning_rate,
                batch_size=args.batch_size,
                max_epochs=args.max_epochs,
                save_per_epochs=args.save_per_epochs,
                num_workers=args.num_workers,
                resume_from_checkpoint=args.resume_from_checkpoint,
                wandb_project=args.wandb_project
            )
            
            if success:
                print(f"🎉 F5-TTS fine-tuning completed successfully!")
                print(f"📁 Model saved to: {args.output_dir}")
                return 0
            else:
                print("❌ Fine-tuning failed!")
                return 1
        else:
            print("✅ Dataset preparation completed. Use --skip_preparation to start training.")
            return 0
            
    except KeyboardInterrupt:
        print("\n⚠️  Training interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
