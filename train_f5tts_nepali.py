#!/usr/bin/env python3
"""
Simple F5-TTS Training Script for Nepali Dataset
"""

import os
import sys
import json
from pathlib import Path

def main():
    """Main training function."""
    
    # Load configuration
    config_file = "f5tts_training_config.json"
    if not Path(config_file).exists():
        print(f"Configuration file not found: {config_file}")
        print("Please run setup_f5tts_training.py first to create the configuration.")
        return 1
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("🚀 Starting F5-TTS Fine-tuning for Nepali Dataset")
    print(f"📁 Dataset: {config['data']['dataset_dir']}")
    print(f"📁 Output: {config['output']['output_dir']}")
    print(f"🎯 Epochs: {config['training']['max_epochs']}")
    print(f"📊 Batch Size: {config['training']['batch_size']}")
    print(f"🧠 Learning Rate: {config['training']['learning_rate']}")
    
    # Create output directories
    output_dir = Path(config['output']['output_dir'])
    checkpoint_dir = Path(config['output']['checkpoint_dir'])
    log_dir = Path(config['output']['log_dir'])
    
    output_dir.mkdir(parents=True, exist_ok=True)
    checkpoint_dir.mkdir(parents=True, exist_ok=True)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Check if dataset exists
    dataset_dir = Path(config['data']['dataset_dir'])
    if not dataset_dir.exists():
        print(f"❌ Dataset directory not found: {dataset_dir}")
        print("Please prepare the dataset first using f5tts_finetune_nepali.py --prepare_only")
        return 1
    
    # Check for required files
    required_files = ['raw.arrow', 'duration.json', 'vocab.txt']
    missing_files = []
    for file in required_files:
        if not (dataset_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files in dataset: {missing_files}")
        print("Please prepare the dataset first using f5tts_finetune_nepali.py --prepare_only")
        return 1
    
    print("✅ All required files found. Dataset is ready for training.")
    
    try:
        # Import F5-TTS training modules
        from f5_tts.train.finetune_cli import main as finetune_main
        
        # Set up arguments for F5-TTS CLI
        args = [
            "--dataset_name", str(dataset_dir),
            "--learning_rate", str(config['training']['learning_rate']),
            "--batch_size_per_gpu", str(config['training']['batch_size']),
            "--epochs", str(config['training']['max_epochs']),
            "--save_per_updates", str(config['training']['save_per_epochs'] * 100),  # Convert epochs to updates
            "--finetune",
            "--tokenizer", "char",
        ]
        
        print(f"🔧 Training arguments: {' '.join(args)}")
        
        # Run training
        original_argv = sys.argv
        sys.argv = ['train'] + args
        
        try:
            finetune_main()
            print("🎉 Training completed successfully!")
            return 0
        finally:
            sys.argv = original_argv
            
    except ImportError as e:
        print(f"❌ Error importing F5-TTS: {e}")
        print("Make sure F5-TTS is properly installed.")
        return 1
    except Exception as e:
        print(f"❌ Training failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
