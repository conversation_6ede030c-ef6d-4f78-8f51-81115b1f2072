#!/usr/bin/env python3
"""
Create a vocabulary file from the Nepali dataset.
"""

import os
from pathlib import Path

def create_vocab_from_dataset(metadata_file, output_vocab_file):
    """
    Create a vocabulary file from the dataset metadata.
    """
    vocab_set = set()
    
    # Read metadata and extract all characters
    with open(metadata_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
                
            parts = line.split('|')
            if len(parts) >= 2:
                text = parts[1].strip()
                # Add all characters from the text to vocab set
                vocab_set.update(list(text))
    
    # Sort vocabulary and write to file
    sorted_vocab = sorted(vocab_set)
    
    with open(output_vocab_file, 'w', encoding='utf-8') as f:
        for char in sorted_vocab:
            f.write(char + '\n')
    
    print(f"Created vocabulary file with {len(sorted_vocab)} characters")
    print(f"Vocabulary saved to: {output_vocab_file}")
    
    return len(sorted_vocab)

def create_vocab_for_prepared_dataset(prepared_dir):
    """
    Create vocab.txt in the prepared dataset directory.
    """
    prepared_path = Path(prepared_dir)
    vocab_file = prepared_path / "vocab.txt"
    
    # Use the original metadata to create vocab
    metadata_file = "nepali_test_subset/metadata.csv"
    
    if Path(metadata_file).exists():
        vocab_size = create_vocab_from_dataset(metadata_file, vocab_file)
        return vocab_size
    else:
        print(f"Metadata file not found: {metadata_file}")
        return 0

if __name__ == "__main__":
    # Create vocab for the prepared test dataset
    vocab_size = create_vocab_for_prepared_dataset("nepali_test_prepared")
    
    if vocab_size > 0:
        print(f"✅ Vocabulary created successfully with {vocab_size} characters!")
    else:
        print("❌ Failed to create vocabulary file")
