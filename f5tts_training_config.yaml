data:
  dataset_dir: nepali_dataset_f5tts_prepared
  max_duration: 30.0
  min_duration: 0.4
  num_workers: 4
  pin_memory: true
  sample_rate: 22050
hardware:
  gpus: 1
  precision: 16
  strategy: auto
logging:
  enable_wandb: false
  log_every_n_steps: 50
  val_check_interval: 0.5
  wandb_project: f5tts-nepali-finetune
model:
  dropout: 0.1
  hidden_dim: 1024
  name: F5-TTS
  num_heads: 16
  num_layers: 12
  vocab_size: 56
output:
  checkpoint_dir: f5tts_nepali_finetune_output/checkpoints
  log_dir: f5tts_nepali_finetune_output/logs
  output_dir: f5tts_nepali_finetune_output
  save_top_k: 3
training:
  accumulate_grad_batches: 1
  batch_size: 8
  gradient_clip_val: 1.0
  learning_rate: 0.0001
  max_epochs: 10
  save_per_epochs: 2
  validation_split: 0.1
  warmup_steps: 1000
