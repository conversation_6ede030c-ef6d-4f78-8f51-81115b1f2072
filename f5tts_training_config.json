{"model": {"name": "F5-TTS", "vocab_size": 56, "hidden_dim": 1024, "num_layers": 12, "num_heads": 16, "dropout": 0.1}, "training": {"learning_rate": 0.0001, "batch_size": 8, "max_epochs": 10, "warmup_steps": 1000, "gradient_clip_val": 1.0, "accumulate_grad_batches": 1, "save_per_epochs": 2, "validation_split": 0.1}, "data": {"dataset_dir": "nepali_full_prepared", "sample_rate": 22050, "max_duration": 30.0, "min_duration": 0.4, "num_workers": 4, "pin_memory": true}, "output": {"output_dir": "f5tts_nepali_finetune_output", "checkpoint_dir": "f5tts_nepali_finetune_output/checkpoints", "log_dir": "f5tts_nepali_finetune_output/logs", "save_top_k": 3}, "logging": {"log_every_n_steps": 50, "val_check_interval": 0.5, "enable_wandb": false, "wandb_project": "f5tts-nepali-finetune"}, "hardware": {"gpus": 1, "precision": 16, "strategy": "auto"}}