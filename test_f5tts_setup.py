#!/usr/bin/env python3
"""
Test F5-TTS Setup and Installation
"""

import os
import sys
from pathlib import Path

def test_f5tts_import():
    """Test if F5-TTS can be imported correctly."""
    print("🔍 Testing F5-TTS import...")
    
    try:
        import f5_tts
        print("✅ F5-TTS imported successfully")
        
        # Test specific modules
        from f5_tts.api import F5TTS
        print("✅ F5TTS API imported successfully")
        
        from f5_tts.train.finetune_cli import main as finetune_main
        print("✅ F5-TTS fine-tuning CLI imported successfully")
        
        from f5_tts.train.datasets.prepare_csv_wavs import prepare_csv_wavs_dir
        print("✅ F5-TTS dataset preparation imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ F5-TTS import failed: {e}")
        return False

def test_dataset_format():
    """Test if our dataset is in the correct format."""
    print("\n🔍 Testing dataset format...")
    
    test_dataset = Path("nepali_test_prepared")
    
    if not test_dataset.exists():
        print(f"❌ Test dataset directory not found: {test_dataset}")
        return False
    
    # Check required files
    required_files = ["raw.arrow", "duration.json", "vocab.txt"]
    missing_files = []
    
    for file in required_files:
        file_path = test_dataset / file
        if not file_path.exists():
            missing_files.append(file)
        else:
            print(f"✅ Found required file: {file}")
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    print("✅ All required dataset files found")
    return True

def test_vocab_file():
    """Test the vocabulary file."""
    print("\n🔍 Testing vocabulary file...")
    
    vocab_file = Path("nepali_test_prepared/vocab.txt")
    
    if not vocab_file.exists():
        print(f"❌ Vocabulary file not found: {vocab_file}")
        return False
    
    try:
        with open(vocab_file, 'r', encoding='utf-8') as f:
            vocab_lines = f.readlines()
        
        vocab_size = len(vocab_lines)
        print(f"✅ Vocabulary file loaded successfully")
        print(f"📊 Vocabulary size: {vocab_size} characters")
        
        # Show first few characters
        if vocab_size > 0:
            sample_chars = [line.strip() for line in vocab_lines[:10]]
            print(f"📝 Sample characters: {sample_chars}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading vocabulary file: {e}")
        return False

def test_f5tts_cli():
    """Test F5-TTS CLI functionality."""
    print("\n🔍 Testing F5-TTS CLI...")
    
    try:
        # Test if we can run the CLI help
        import subprocess
        result = subprocess.run([
            sys.executable, "-m", "f5_tts.train.finetune_cli", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ F5-TTS CLI is accessible")
            return True
        else:
            print(f"❌ F5-TTS CLI failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing F5-TTS CLI: {e}")
        return False

def test_inference_capability():
    """Test basic F5-TTS inference capability."""
    print("\n🔍 Testing F5-TTS inference capability...")
    
    try:
        from f5_tts.api import F5TTS
        
        # Try to initialize F5TTS (this will test if the model can be loaded)
        print("🔄 Initializing F5TTS model...")
        
        # Note: This might download the pretrained model if not available
        f5tts = F5TTS(model_type="F5-TTS", ckpt_file="", vocab_file="")
        print("✅ F5TTS model initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"⚠️  F5TTS inference test skipped: {e}")
        print("   (This is normal if no pretrained model is available)")
        return True  # Don't fail the test for this

def main():
    """Run all tests."""
    print("🧪 F5-TTS Setup Test Suite")
    print("=" * 50)
    
    tests = [
        ("F5-TTS Import", test_f5tts_import),
        ("Dataset Format", test_dataset_format),
        ("Vocabulary File", test_vocab_file),
        ("F5-TTS CLI", test_f5tts_cli),
        ("Inference Capability", test_inference_capability),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*50}")
    print(f"🧪 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! F5-TTS setup is ready for fine-tuning.")
        print("\n📋 Next steps:")
        print("1. Prepare your full dataset:")
        print("   ./f5-tts-env/bin/python f5tts_finetune_nepali.py --prepare_only")
        print("\n2. Start fine-tuning:")
        print("   ./f5-tts-env/bin/python f5tts_finetune_nepali.py")
        return 0
    else:
        print(f"⚠️  {total - passed} test(s) failed. Please check the setup.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
